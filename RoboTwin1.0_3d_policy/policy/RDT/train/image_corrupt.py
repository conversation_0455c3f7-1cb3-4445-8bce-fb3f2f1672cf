import warnings
warnings.simplefilter(action='ignore', category=FutureWarning)

import numpy as np
np.bool = np.bool_
import imgaug.augmenters as iaa
from PIL import Image


# Define our sequence of augmentation steps that will be applied to every image.
seq = iaa.Sequential(
    [
        # Execute one of the following noise augmentations
        iaa.OneOf([
            iaa.AdditiveGaussianNoise(
                loc=0, scale=(0.0, 0.05*255), per_channel=0.5
            ),
            iaa.AdditiveLaplaceNoise(scale=(0.0, 0.05*255), per_channel=0.5),
            iaa.AdditivePoissonNoise(lam=(0.0, 0.05*255), per_channel=0.5)
        ]),
        
        # Execute one or none of the following blur augmentations
        iaa.SomeOf((0, 1), [
            iaa.OneOf([
                iaa.GaussianBlur((0, 3.0)),
                iaa.AverageBlur(k=(2, 7)),
                iaa.MedianBlur(k=(3, 11)),
            ]),
            iaa.MotionBlur(k=(3, 36)),
        ]),
    ],
    # do all of the above augmentations in random order
    random_order=True
)


def image_corrupt(image: Image):
    image_arr = np.array(image)
    image_arr = image_arr[None, ...]
    
    image_arr = seq(images=image_arr)
    
    image = Image.fromarray(image_arr[0])
    return image
