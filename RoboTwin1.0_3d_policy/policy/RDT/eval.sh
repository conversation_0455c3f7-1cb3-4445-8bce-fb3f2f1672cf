DEBUG=False

task_name=${1}
head_camera_type=${2}
model_name=${3}
checkpoint_id=${4}
seed=${5}
gpu_id=${6}

export HYDRA_FULL_ERROR=1
export CUDA_VISIBLE_DEVICES=${gpu_id}

cd ../..
# example 
# python ./script/eval_policy_rdt.py shoe_place D435 sp_robotwin 20000 1
python ./script/eval_policy_rdt.py $task_name $head_camera_type $model_name $checkpoint_id $seed                                                                                                                                            