import sys
sys.path.append('./')

import sapien.core as sapien
from collections import OrderedDict
import pdb
from envs import *
import yaml
import importlib
import json
import traceback
import os

current_file_path = os.path.abspath(__file__)
parent_directory = os.path.dirname(current_file_path)

def class_decorator(task_name):
    envs_module = importlib.import_module(f'envs.{task_name}')
    try:
        env_class = getattr(envs_module, task_name)
        env_instance = env_class()
    except ModuleNotFoundError:
        raise SystemExit("No such task")
    return env_instance

def get_camera_config(camera_type):
    camera_config_path = os.path.join(parent_directory, '../task_config/_camera_config.yml')

    assert os.path.isfile(camera_config_path), "task config file is missing"

    with open(camera_config_path, 'r', encoding='utf-8') as f:
        args = yaml.load(f.read(), Loader=yaml.FullLoader)

    assert camera_type in args, f'camera {camera_type} is not defined'
    return args[camera_type]

def main():
    task_name = input()
    
    task = class_decorator(task_name)
    task_config_path = f'./task_config/{task_name}.yml'

    assert os.path.isfile(task_config_path), "task config file is missing"

    with open(task_config_path, 'r', encoding='utf-8') as f:
        args = yaml.load(f.read(), Loader=yaml.FullLoader)

    head_camera_config = get_camera_config(args['head_camera_type'])
    args['head_camera_fovy'] = head_camera_config['fovy']
    args['head_camera_w'] = head_camera_config['w']
    args['head_camera_h'] = head_camera_config['h']
    head_camera_config = 'fovy' + str(args['head_camera_fovy']) + '_w' + str(args['head_camera_w']) + '_h' + str(args['head_camera_h'])
    
    wrist_camera_config = get_camera_config(args['wrist_camera_type'])
    args['wrist_camera_fovy'] = wrist_camera_config['fovy']
    args['wrist_camera_w'] = wrist_camera_config['w']
    args['wrist_camera_h'] = wrist_camera_config['h']
    wrist_camera_config = 'fovy' + str(args['wrist_camera_fovy']) + '_w' + str(args['wrist_camera_w']) + '_h' + str(args['wrist_camera_h'])

    front_camera_config = get_camera_config(args['front_camera_type'])
    args['front_camera_fovy'] = front_camera_config['fovy']
    args['front_camera_w'] = front_camera_config['w']
    args['front_camera_h'] = front_camera_config['h']
    front_camera_config = 'fovy' + str(args['front_camera_fovy']) + '_w' + str(args['front_camera_w']) + '_h' + str(args['front_camera_h'])

    # output camera config
    print('============= Camera Config =============\n')
    print('Head Camera Config:\n    type: '+ str(args['head_camera_type']) + '\n    fovy: ' + str(args['head_camera_fovy']) + '\n    camera_w: ' + str(args['head_camera_w']) + '\n    camera_h: ' + str(args['head_camera_h']))
    print('Wrist Camera Config:\n    type: '+ str(args['wrist_camera_type']) + '\n    fovy: ' + str(args['wrist_camera_fovy']) + '\n    camera_w: ' + str(args['wrist_camera_w']) + '\n    camera_h: ' + str(args['wrist_camera_h']))
    print('Front Camera Config:\n    type: '+ str(args['front_camera_type']) + '\n    fovy: ' + str(args['front_camera_fovy']) + '\n    camera_w: ' + str(args['front_camera_w']) + '\n    camera_h: ' + str(args['front_camera_h']))
    print('\n=======================================')

    args['save_path'] += '/' + str(args['task_name']) + '_' + str(args['head_camera_type'])
    run(task, args)


def run(Demo_class, args):
    epid = 0       
    seed_list=[]   
    suc_num = 0    
    fail_num = 0   
    print(f"Task name: {args['task_name']}")

    if not args['use_seed']:
        while suc_num < args['episode_num']:
            try:
                Demo_class.setup_demo(now_ep_num=suc_num, seed = epid, **args)
                Demo_class.play_once()

                if Demo_class.plan_success and Demo_class.check_success():
                    print(f"simulate data episode {suc_num} success! (seed = {epid})")
                    seed_list.append(epid)
                    suc_num+=1
                else:
                    print(f"simulate data episode {suc_num} fail! (seed = {epid})   ")
                    fail_num +=1
                
                Demo_class.close()
                if (args['render_freq']):
                    Demo_class.viewer.close()
                epid +=1
            except Exception as e:
                stack_trace = traceback.format_exc()
                print(' -------------')
                print(f"simulate data episode {suc_num} fail! (seed = {epid})   ")
                print('Error: ', stack_trace)
                print(' -------------')
                fail_num +=1
                Demo_class.close()
                if (args['render_freq']):
                    Demo_class.viewer.close()
                epid +=1
        
        with open('./task_config/seeds/'+args['task_name']+'.txt', 'w') as file:
            for sed in seed_list:
                file.write("%s " % sed)
        print(f'\nComplete simulation, failed {fail_num} times')

    else:
        print(f'using saved seeds list')
        with open('./task_config/seeds/'+args['task_name']+'.txt', 'r') as file:
            seed_list = file.read().split()
            seed_list = [int(i) for i in seed_list]

    if args['collect_data']:
        print('Start data collection')

        args['render_freq']=0
        args['is_save'] = True

        for id in range(args['st_episode'], args['episode_num']):
            Demo_class.setup_demo(now_ep_num=id, seed = seed_list[id],**args)
            # modify start
            info_file_path = f'./data/'+args['task_name']+'_'+str(args['head_camera_type'])+'_pkl/scene_info.json'
            os.makedirs(f'./data/'+args['task_name']+'_'+str(args['head_camera_type'])+'_pkl', exist_ok=True)

            if not os.path.exists(info_file_path):
                with open(info_file_path, 'w', encoding='utf-8') as file:
                    json.dump({}, file, ensure_ascii=False)

            with open(info_file_path, 'r', encoding='utf-8') as file:
                info_db = json.load(file)

            info = Demo_class.play_once()
            info_db[f'{id}'] = info
            with open(info_file_path, 'w', encoding='utf-8') as file:
                json.dump(info_db, file, ensure_ascii=False)

            if Demo_class.save_type.get('raw_data', True):
                head_config = Demo_class.get_camera_config(Demo_class.head_camera)
                left_config = Demo_class.get_camera_config(Demo_class.left_camera)
                right_config = Demo_class.get_camera_config(Demo_class.right_camera)
                save_json(Demo_class.file_path["f_color"]+"config.json", head_config)
                save_json(Demo_class.file_path["l_color"]+"config.json", left_config)
                save_json(Demo_class.file_path["r_color"]+"config.json", right_config)

                save_json(Demo_class.file_path["f_depth"]+"config.json", head_config)
                save_json(Demo_class.file_path["l_depth"]+"config.json", left_config)
                save_json(Demo_class.file_path["r_depth"]+"config.json", right_config)

                save_json(Demo_class.file_path["f_pcd"]+"config.json", head_config)
                save_json(Demo_class.file_path["l_pcd"]+"config.json", left_config)
                save_json(Demo_class.file_path["r_pcd"]+"config.json", right_config)

            Demo_class.close()
            print('\nsuccess!')

            
if __name__ == "__main__":
    from test_render import Sapien_TEST
    Sapien_TEST()
    main()
