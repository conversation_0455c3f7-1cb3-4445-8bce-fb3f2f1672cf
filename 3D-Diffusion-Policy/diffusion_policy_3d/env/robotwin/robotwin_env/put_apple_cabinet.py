from .base_task import Base_task
from .utils import *
import sapien


class put_apple_cabinet(Base_task):
    def setup_demo(self,**kwags):
        super()._init(**kwags,table_static=False)
        self.create_table_and_wall()
        self.load_robot()
        self.setup_planner()
        self.load_camera()
        self.pre_move()
        self.load_actors()
        self.step_lim = 600

    def pre_move(self):
        render_freq = self.render_freq
        self.render_freq=0
        self.together_open_gripper(save_freq=None)
        self.render_freq = render_freq
    
    def load_actors(self):
        self.cabinet, _ = rand_create_urdf_obj(
            self.scene,
            modelname="036_cabine",
            xlim=[0,0],
            ylim=[0.155,0.155],
            zlim=[0.96],
            rotate_rand=False,
            qpos=[1,0,0,1],
            scale=0.27
        )

        self.cabinet_active_joints = self.cabinet.get_active_joints()
        for joint in self.cabinet_active_joints:
            # set stiffness to 0 to avoid restoring elastic force
            joint.set_drive_property(stiffness=0, damping=5, force_limit=1000, mode="force")
        self.cabinet_all_joints = self.cabinet.get_joints()

        self.apple,_ = rand_create_obj(
            self.scene,
            xlim=[0.2,0.32],
            ylim=[-0.2,-0.1],
            zlim=[0.78],
            modelname="035_apple",
            rotate_rand=False,
            convex=True
        )
        self.apple.find_component_by_type(sapien.physx.PhysxRigidDynamicComponent).mass = 0.01
        
    def play_once(self):
        pose0 = list(self.cabinet.get_pose().p+[-0.054,-0.37,-0.09])+[0.5,0.5,0.5,0.5]
        pose1 = list(self.apple.get_pose().p+[0,0,0.17])+[-0.5,0.5,-0.5,-0.5]
        self.together_move_to_pose_with_screw(left_target_pose=pose0,right_target_pose=pose1)
        pose0[1] +=0.09
        pose1[2] -= 0.05
        self.together_move_to_pose_with_screw(left_target_pose=pose0,right_target_pose=pose1)

        self.together_close_gripper(left_pos=-0.02)
        pose0[1]-=0.18
        pose1[2]+=0.18
        self.together_move_to_pose_with_screw(left_target_pose=pose0,right_target_pose=pose1)
        pose2 = list(self.cabinet.get_pose().p+[0.036,-0.216,0.078]) + [-0.5,0.5,-0.5,-0.5]
        pose1[1] = pose2[1]
        self.right_move_to_pose_with_screw(pose1)
        self.right_move_to_pose_with_screw(pose2)

        self.open_right_gripper()
        pose2[2]+=0.082
        self.right_move_to_pose_with_screw(pose1)
        pose0[1]+=0.18

        self.together_move_to_pose_with_screw(left_target_pose=pose0,right_target_pose=self.right_original_pose)
        
    def check_success(self):
        cabinet_pos = self.cabinet.get_pose().p
        apple_pose = self.apple.get_pose().p
        left_endpose = self.get_left_endpose_pose()
        target_pose = (cabinet_pos + np.array([-0.05,-0.27,-0.09])).tolist() + [0.5, -0.5, -0.5, 0.5]
        eps1 = 0.03
        tag = np.all(abs(apple_pose[:2] - np.array([0.01, 0.1])) < np.array([0.015,0.015]))
        return np.abs(apple_pose[2] - 0.797) < 0.015 and tag and\
               np.all(abs(np.array(left_endpose.p.tolist() + left_endpose.q.tolist()) - target_pose) < eps1)
