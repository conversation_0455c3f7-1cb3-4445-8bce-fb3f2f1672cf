name: realdex_dumpling

task_name: dumpling

shape_meta: &shape_meta
  # acceptable types: rgb, low_dim
  obs:
    point_cloud:
      shape: [1024, 3]
      type: point_cloud
    agent_pos:
      shape: [22]
      type: low_dimx
  action:
    shape: [22]

env_runner: null

dataset:
  _target_: diffusion_policy_3d.dataset.realdex_dataset.RealDexDataset
  zarr_path: data/realdex_dumpling.zarr
  horizon: ${horizon}
  pad_before: ${eval:'${n_obs_steps}-1'}
  pad_after: ${eval:'${n_action_steps}-1'}
  seed: 42
  val_ratio: 0.02
  max_train_episodes: 90
